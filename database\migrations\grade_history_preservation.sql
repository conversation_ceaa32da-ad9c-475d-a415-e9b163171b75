-- Grade History Preservation Migration
-- This migration modifies the database to preserve student grade history
-- when students are promoted or moved to alumni status

-- Step 1: Add alumni_id to relevant tables to maintain references (if not exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'alumni'
     AND COLUMN_NAME = 'siswa_id') = 0,
    'ALTER TABLE `alumni` ADD COLUMN `siswa_id` INT(11) NULL AFTER `id`',
    'SELECT "Column siswa_id already exists in alumni table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for alumni.siswa_id if column was added
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'alumni'
     AND INDEX_NAME = 'idx_siswa_id') = 0,
    'ALTER TABLE `alumni` ADD INDEX `idx_siswa_id` (`siswa_id`)',
    'SELECT "Index idx_siswa_id already exists on alumni table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 2: Add status column to siswa table to track student status (if not exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'siswa'
     AND COLUMN_NAME = 'status') = 0,
    'ALTER TABLE `siswa` ADD COLUMN `status` ENUM(''aktif'', ''alumni'', ''pindah'') NOT NULL DEFAULT ''aktif'' AFTER `kelas_id`',
    'SELECT "Column status already exists in siswa table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add alumni_id column to siswa table (if not exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'siswa'
     AND COLUMN_NAME = 'alumni_id') = 0,
    'ALTER TABLE `siswa` ADD COLUMN `alumni_id` INT(11) NULL AFTER `status`',
    'SELECT "Column alumni_id already exists in siswa table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add indexes for siswa table
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'siswa'
     AND INDEX_NAME = 'idx_status') = 0,
    'ALTER TABLE `siswa` ADD INDEX `idx_status` (`status`)',
    'SELECT "Index idx_status already exists on siswa table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'siswa'
     AND INDEX_NAME = 'idx_alumni_id') = 0,
    'ALTER TABLE `siswa` ADD INDEX `idx_alumni_id` (`alumni_id`)',
    'SELECT "Index idx_alumni_id already exists on siswa table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 3: Modify foreign key constraints to prevent cascading deletions
-- Drop existing constraints that cause data loss
ALTER TABLE `nilai` DROP FOREIGN KEY `nilai_ibfk_1`;
ALTER TABLE `nilai_sikap` DROP FOREIGN KEY `nilai_sikap_ibfk_1`;
ALTER TABLE `nilai_tugas` DROP FOREIGN KEY `nilai_tugas_ibfk_2`;
ALTER TABLE `detail_absensi` DROP FOREIGN KEY `detail_absensi_ibfk_2`;
ALTER TABLE `tugas_tambahan_siswa` DROP FOREIGN KEY `tugas_tambahan_siswa_ibfk_2`;
ALTER TABLE `riwayat_kelas` DROP FOREIGN KEY `riwayat_kelas_ibfk_1`;

-- Add new constraints that preserve data
ALTER TABLE `nilai` ADD CONSTRAINT `nilai_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `nilai_sikap` ADD CONSTRAINT `nilai_sikap_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `nilai_tugas` ADD CONSTRAINT `nilai_tugas_ibfk_2` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `detail_absensi` ADD CONSTRAINT `detail_absensi_ibfk_2` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `tugas_tambahan_siswa` ADD CONSTRAINT `tugas_tambahan_siswa_ibfk_2` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `riwayat_kelas` ADD CONSTRAINT `riwayat_kelas_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Step 4: Create alumni-siswa relationship
ALTER TABLE `alumni` ADD CONSTRAINT `alumni_siswa_fk` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `siswa` ADD CONSTRAINT `siswa_alumni_fk` FOREIGN KEY (`alumni_id`) REFERENCES `alumni` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Step 5: Create view for complete student academic records
CREATE OR REPLACE VIEW `v_student_academic_record` AS
SELECT 
    s.id as siswa_id,
    s.nis,
    s.nama_siswa,
    s.jenis_kelamin,
    s.status as student_status,
    s.alamat,
    s.no_telp,
    k.nama_kelas as current_kelas,
    a.tahun_lulus,
    a.kelas_terakhir,
    -- Grade information
    n.id as nilai_id,
    n.mapel_id,
    mp.nama_mapel,
    n.nilai_tugas,
    n.nilai_uts,
    n.nilai_uas,
    n.nilai_absen,
    n.nilai_akhir,
    n.semester,
    n.tahun_ajaran,
    -- Class history
    rk.kelas_id as historical_kelas_id,
    k_hist.nama_kelas as historical_kelas_name,
    rk.status as class_status
FROM siswa s
LEFT JOIN kelas k ON s.kelas_id = k.id
LEFT JOIN alumni a ON s.alumni_id = a.id
LEFT JOIN nilai n ON s.id = n.siswa_id
LEFT JOIN mata_pelajaran mp ON n.mapel_id = mp.id
LEFT JOIN riwayat_kelas rk ON s.id = rk.siswa_id
LEFT JOIN kelas k_hist ON rk.kelas_id = k_hist.id;

-- Step 6: Create view for alumni with complete academic records
CREATE OR REPLACE VIEW `v_alumni_academic_record` AS
SELECT 
    a.id as alumni_id,
    a.nis,
    a.nama_siswa,
    a.jenis_kelamin,
    a.alamat,
    a.no_telp,
    a.tahun_lulus,
    a.kelas_terakhir,
    s.id as siswa_id,
    -- Grade information
    n.id as nilai_id,
    n.mapel_id,
    mp.nama_mapel,
    n.nilai_tugas,
    n.nilai_uts,
    n.nilai_uas,
    n.nilai_absen,
    n.nilai_akhir,
    n.semester,
    n.tahun_ajaran,
    -- Class history
    rk.kelas_id as historical_kelas_id,
    k_hist.nama_kelas as historical_kelas_name,
    rk.status as class_status
FROM alumni a
LEFT JOIN siswa s ON a.siswa_id = s.id
LEFT JOIN nilai n ON s.id = n.siswa_id
LEFT JOIN mata_pelajaran mp ON n.mapel_id = mp.id
LEFT JOIN riwayat_kelas rk ON s.id = rk.siswa_id
LEFT JOIN kelas k_hist ON rk.kelas_id = k_hist.id;

-- Step 7: Create indexes for better performance
CREATE INDEX `idx_siswa_status_nis` ON `siswa` (`status`, `nis`);
CREATE INDEX `idx_nilai_siswa_semester_tahun` ON `nilai` (`siswa_id`, `semester`, `tahun_ajaran`);
CREATE INDEX `idx_riwayat_kelas_siswa_tahun` ON `riwayat_kelas` (`siswa_id`, `tahun_ajaran`);

-- Step 8: Update existing alumni records to maintain relationships
-- This will be handled in the application code during migration
