<?php
/**
 * Grade History Preservation Migration Script
 * 
 * This script safely migrates the database to preserve student grade history
 * when students are promoted or moved to alumni status.
 * 
 * IMPORTANT: Backup your database before running this migration!
 */

require_once __DIR__ . '/../../config/database.php';

class GradeHistoryMigration {
    private $conn;
    private $log = [];
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    public function run() {
        try {
            $this->log("Starting Grade History Preservation Migration...");
            
            // Start transaction
            $this->conn->beginTransaction();
            
            // Step 1: Add new columns
            $this->addNewColumns();
            
            // Step 2: Update existing data
            $this->updateExistingData();
            
            // Step 3: Modify foreign key constraints
            $this->modifyForeignKeyConstraints();
            
            // Step 4: <PERSON>reate views
            $this->createViews();
            
            // Step 5: Create indexes
            $this->createIndexes();
            
            // Commit transaction
            $this->conn->commit();
            
            $this->log("Migration completed successfully!");
            return true;
            
        } catch (Exception $e) {
            // Rollback on error if transaction is active
            if ($this->conn->inTransaction()) {
                $this->conn->rollback();
            }
            $this->log("Migration failed: " . $e->getMessage());
            return false;
        }
    }
    
    private function addNewColumns() {
        $this->log("Adding new columns...");

        // Add siswa_id to alumni table if not exists
        if (!$this->columnExists('alumni', 'siswa_id')) {
            $this->executeQuery("ALTER TABLE `alumni` ADD COLUMN `siswa_id` INT(11) NULL AFTER `id`");
            $this->log("Added siswa_id column to alumni table");
        } else {
            $this->log("Column siswa_id already exists in alumni table");
        }

        // Add status to siswa table if not exists
        if (!$this->columnExists('siswa', 'status')) {
            $this->executeQuery("ALTER TABLE `siswa` ADD COLUMN `status` ENUM('aktif', 'alumni', 'pindah') NOT NULL DEFAULT 'aktif' AFTER `kelas_id`");
            $this->log("Added status column to siswa table");
        } else {
            $this->log("Column status already exists in siswa table");
        }

        // Add alumni_id to siswa table if not exists
        if (!$this->columnExists('siswa', 'alumni_id')) {
            $this->executeQuery("ALTER TABLE `siswa` ADD COLUMN `alumni_id` INT(11) NULL AFTER `status`");
            $this->log("Added alumni_id column to siswa table");
        } else {
            $this->log("Column alumni_id already exists in siswa table");
        }

        $this->log("New columns processing completed.");
    }
    
    private function updateExistingData() {
        $this->log("Updating existing data...");
        
        // Get all current alumni records
        $stmt = $this->conn->query("SELECT * FROM alumni");
        $alumni_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($alumni_records as $alumni) {
            // Try to find matching siswa record that might have been deleted
            // For now, we'll create placeholder siswa records for existing alumni
            $this->createPlaceholderSiswaForAlumni($alumni);
        }
        
        $this->log("Existing data updated successfully.");
    }
    
    private function createPlaceholderSiswaForAlumni($alumni) {
        // Check if siswa record exists
        $stmt = $this->conn->prepare("SELECT id FROM siswa WHERE nis = ?");
        $stmt->execute([$alumni['nis']]);
        $siswa = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$siswa) {
            // Create placeholder siswa record
            $stmt = $this->conn->prepare("
                INSERT INTO siswa (nis, nama_siswa, jenis_kelamin, kelas_id, status, alumni_id, alamat, no_telp) 
                VALUES (?, ?, ?, 1, 'alumni', ?, ?, ?)
            ");
            $stmt->execute([
                $alumni['nis'],
                $alumni['nama_siswa'],
                $alumni['jenis_kelamin'],
                $alumni['id'],
                $alumni['alamat'],
                $alumni['no_telp']
            ]);
            
            $siswa_id = $this->conn->lastInsertId();
            
            // Update alumni record with siswa_id
            $stmt = $this->conn->prepare("UPDATE alumni SET siswa_id = ? WHERE id = ?");
            $stmt->execute([$siswa_id, $alumni['id']]);
            
            $this->log("Created placeholder siswa record for alumni: " . $alumni['nama_siswa']);
        } else {
            // Update existing siswa record
            $stmt = $this->conn->prepare("UPDATE siswa SET status = 'alumni', alumni_id = ? WHERE id = ?");
            $stmt->execute([$alumni['id'], $siswa['id']]);
            
            // Update alumni record with siswa_id
            $stmt = $this->conn->prepare("UPDATE alumni SET siswa_id = ? WHERE id = ?");
            $stmt->execute([$siswa['id'], $alumni['id']]);
            
            $this->log("Updated existing siswa record for alumni: " . $alumni['nama_siswa']);
        }
    }
    
    private function modifyForeignKeyConstraints() {
        $this->log("Modifying foreign key constraints...");

        // Drop existing CASCADE constraints
        $constraints_to_drop = [
            'nilai' => 'nilai_ibfk_1',
            'nilai_sikap' => 'nilai_sikap_ibfk_1',
            'nilai_tugas' => 'nilai_tugas_ibfk_2',
            'detail_absensi' => 'detail_absensi_ibfk_2',
            'tugas_tambahan_siswa' => 'tugas_tambahan_siswa_ibfk_2',
            'riwayat_kelas' => 'riwayat_kelas_ibfk_1'
        ];

        foreach ($constraints_to_drop as $table => $constraint) {
            try {
                $this->executeQuery("ALTER TABLE `{$table}` DROP FOREIGN KEY `{$constraint}`");
                $this->log("Dropped foreign key constraint {$constraint} from {$table}");
            } catch (Exception $e) {
                $this->log("Foreign key constraint {$constraint} not found in {$table} (might not exist)");
            }
        }

        // Add new RESTRICT constraints
        $constraints_to_add = [
            "ALTER TABLE `nilai` ADD CONSTRAINT `nilai_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE",
            "ALTER TABLE `nilai_sikap` ADD CONSTRAINT `nilai_sikap_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE",
            "ALTER TABLE `nilai_tugas` ADD CONSTRAINT `nilai_tugas_ibfk_2` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE",
            "ALTER TABLE `detail_absensi` ADD CONSTRAINT `detail_absensi_ibfk_2` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE",
            "ALTER TABLE `tugas_tambahan_siswa` ADD CONSTRAINT `tugas_tambahan_siswa_ibfk_2` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE",
            "ALTER TABLE `riwayat_kelas` ADD CONSTRAINT `riwayat_kelas_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE",
            "ALTER TABLE `alumni` ADD CONSTRAINT `alumni_siswa_fk` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE SET NULL ON UPDATE CASCADE",
            "ALTER TABLE `siswa` ADD CONSTRAINT `siswa_alumni_fk` FOREIGN KEY (`alumni_id`) REFERENCES `alumni` (`id`) ON DELETE SET NULL ON UPDATE CASCADE"
        ];

        foreach ($constraints_to_add as $constraint_sql) {
            try {
                $this->executeQuery($constraint_sql);
                $this->log("Added foreign key constraint successfully");
            } catch (Exception $e) {
                $this->log("Foreign key constraint creation failed (might already exist): " . $e->getMessage());
            }
        }

        $this->log("Foreign key constraints processing completed.");
    }
    
    private function createViews() {
        $this->log("Creating database views...");
        
        // Create student academic record view
        $view_sql = file_get_contents(__DIR__ . '/grade_history_preservation.sql');
        $views = explode('CREATE OR REPLACE VIEW', $view_sql);
        
        foreach ($views as $view) {
            if (trim($view) && strpos($view, 'v_student_academic_record') !== false) {
                $this->executeQuery('CREATE OR REPLACE VIEW' . $view);
                break;
            }
        }
        
        foreach ($views as $view) {
            if (trim($view) && strpos($view, 'v_alumni_academic_record') !== false) {
                $this->executeQuery('CREATE OR REPLACE VIEW' . $view);
                break;
            }
        }
        
        $this->log("Database views created successfully.");
    }
    
    private function createIndexes() {
        $this->log("Creating indexes...");
        
        $indexes = [
            "CREATE INDEX `idx_siswa_id` ON `alumni` (`siswa_id`)",
            "CREATE INDEX `idx_status` ON `siswa` (`status`)",
            "CREATE INDEX `idx_alumni_id` ON `siswa` (`alumni_id`)",
            "CREATE INDEX `idx_siswa_status_nis` ON `siswa` (`status`, `nis`)",
            "CREATE INDEX `idx_nilai_siswa_semester_tahun` ON `nilai` (`siswa_id`, `semester`, `tahun_ajaran`)",
            "CREATE INDEX `idx_riwayat_kelas_siswa_tahun` ON `riwayat_kelas` (`siswa_id`, `tahun_ajaran`)"
        ];
        
        foreach ($indexes as $index_sql) {
            try {
                $this->executeQuery($index_sql);
            } catch (Exception $e) {
                // Index might already exist, continue
                $this->log("Index creation skipped (might already exist): " . $e->getMessage());
            }
        }
        
        $this->log("Indexes created successfully.");
    }
    
    private function columnExists($table_name, $column_name) {
        try {
            $query = "SELECT COUNT(*) as count
                     FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = DATABASE()
                     AND TABLE_NAME = :table_name
                     AND COLUMN_NAME = :column_name";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':table_name', $table_name);
            $stmt->bindParam(':column_name', $column_name);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] > 0;

        } catch (Exception $e) {
            $this->log("Column check error: " . $e->getMessage());
            return false;
        }
    }

    private function executeQuery($sql) {
        try {
            $this->conn->exec($sql);
            return true;
        } catch (PDOException $e) {
            throw new Exception("Query failed: {$sql}. Error: " . $e->getMessage());
        }
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] {$message}";
        $this->log[] = $log_entry;
        echo $log_entry . "\n";
    }
    
    public function getLog() {
        return $this->log;
    }
}

// Run migration if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "Grade History Preservation Migration\n";
    echo "====================================\n\n";
    echo "WARNING: This will modify your database structure.\n";
    echo "Please ensure you have backed up your database before proceeding.\n\n";
    
    $migration = new GradeHistoryMigration();
    $success = $migration->run();
    
    if ($success) {
        echo "\nMigration completed successfully!\n";
        exit(0);
    } else {
        echo "\nMigration failed. Please check the error messages above.\n";
        exit(1);
    }
}
?>
